name: pieces_ai
description: Ai动漫视频生成
version: 1.0.26+26
homepage: https://www.pencil-stub.com/#/


environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.8
  cached_network_image: ^3.3.0 #网络图片缓存
  firebase_analytics: ^11.4.4 # Firebase Analytics

  flutter_bloc: ^8.1.6 # 状态管理

  archive: ^3.6.1 # 解压
  package_info_plus: ^8.0.2 # 应用包信息
  webview_flutter: ^4.8.0
  go_router: ^14.2.3
  # sqflite: ^2.3.3+1 # 数据库
  # sqflite_common_ffi: ^2.3.3 # 数据库
  shared_preferences: ^2.3.2 # xml 固化
  path_provider: ^2.1.5 # 路径

  dio: ^5.4.0 # 网络请求
  http: ^1.3.0 # 网络请求
  #  jwt_decoder: ^2.0.1 # jwt 解析token是否过期

  #  refresh: ^1.0.1
  #  toggle_rotate: ^1.0.1
  #  flutter_star: ^1.2.0 # 星星组件
  dash_painter: ^1.0.2
  wrapper: ^1.0.2
  yaml_modify: ^1.0.1

  url_launcher: ^6.3.0 # url
  share_plus: ^10.1.4 # 文字分享

  flutter_svg: ^2.0.7
  intl: ^0.19.0
  platform: ^3.1.0
  image: ^4.0.17
  flutter_spinkit: ^5.2.0 # loading
  flutter_markdown: ^0.6.4 # markdown
  #  file_selector: ^0.9.2+2
  file_picker: ^8.0.7
  #加密相关
  crypto: ^3.0.5
  encrypt: ^5.0.1
  image_compare_2: ^1.1.7
  # 轮播banner
  carousel_slider: ^5.0.0
  # 土司
  motion_toast: ^2.8.0
  # 首页拖拽按钮
#  draggable_widget: ^2.0.0
  json_annotation: ^4.9.0
#  google_fonts: ^6.2.1
  video_player: ^2.9.2
  chewie: ^1.10.0

  just_audio: ^0.9.42

  # 生成二维码
  pretty_qr_code: ^3.3.0
  #图片切割小组件
  crop_your_image: ^1.0.2
  #各种样式的弹出Menu
  #  star_menu: ^4.0.1
  horizontal_list_view: ^1.1.0
  # 动画框架
  #  flutter_animate: ^4.5.0
  simple_animations: ^5.0.2
  # 设备信息
  # device_info_plus: ^10.1.0
  #GIF控制加载
  gif: ^2.3.0
  #laoding框全局单例
  flutter_easyloading: ^3.0.5
  #全局配置文件
  global_configuration: ^2.0.0
  process_run: ^1.1.0
  # Android风格日志
  logger: ^2.3.0
  progress_border: ^0.1.5
  wakelock_plus: ^1.2.8

  # 应用内支付
  in_app_purchase: ^3.2.0
  fraction: ^4.1.4
  image_picker: ^0.8.9

  singular_flutter_sdk: ^1.6.0

  # ios14权限
  app_tracking_transparency: ^2.0.6

  # 三方widget 使用
  flutter_widget_from_html: ^0.16.0
  bottom_navy_bar: ^6.1.0
  persistent_bottom_nav_bar_v2: ^5.3.1
  textfield_tags: ^3.0.1
  buttons_tabbar: ^1.3.13
  font_awesome_flutter: ^10.8.0
  flutter_rating_bar: ^4.0.1
  card_swiper: ^3.0.1
  animated_bottom_navigation_bar: ^1.3.3

  # 投屏
  castscreen: ^1.0.2

  # 使用firebase登录
  firebase_auth: ^5.5.1
  firebase_core: ^3.8.0
  flutter_dotenv:
#  open_filex: ^4.6.0 # 打开文件
  flutter_login: 5.0.0
  google_sign_in: ^6.3.0
  flutter_facebook_auth: ^7.1.1
  supabase_flutter: ^2.8.1
#  supabase_auth_ui: ^0.5.4

  widget_repository:
    path: packages/widget_repository
  storage:
    path: packages/storage
  app:
    path: packages/app
  components:
    path: packages/components
  android_id: ^0.4.0
  connectivity_plus: ^6.1.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0
  build_runner: ^2.4.7
  json_serializable: ^6.8.0

flutter:
  uses-material-design: true
  generate: true
  assets:
    - assets/images/
    - assets/images/icon/
    - assets/images/bg/
    - assets/cfg/
    - assets/audio/
    - assets/data/
    - assets/images/head_icon/
    - assets/images/logo/
    - assets/images/widgets/
    - assets/flutter.db
    - assets/version.json
    - .env

  fonts: # 配置字体，可配置多个，支持ttf和otf,ttc等字体资源
    - family: TolyIconP
      fonts:
        - asset: assets/iconfont/toly_icon_p.ttf
    - family: TolyIcon
      fonts:
        - asset: assets/iconfont/toly_icon.ttf
    - family: IndieFlower #字体名
      fonts:
        - asset: assets/fonts/IndieFlower-Regular.ttf
    - family: BalooBhai2 #字体名
      fonts:
        - asset: assets/fonts/BalooBhai2-Regular.ttf
    - family: Inconsolata #字体名
      fonts:
        - asset: assets/fonts/Inconsolata-Regular.ttf
    - family: Neucha #字体名
      fonts:
        - asset: assets/fonts/Neucha-Regular.ttf
    - family: ComicNeue #字体名
      fonts:
        - asset: assets/fonts/ComicNeue-Regular.ttf
    - family: CHOPS
      fonts:
        - asset: assets/fonts/CHOPS.TTF












